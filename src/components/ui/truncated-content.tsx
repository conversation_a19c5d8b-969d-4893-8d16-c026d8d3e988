"use client";

import { useEffect, useRef, useState } from "react";
import { startViewTransition } from "use-start-view-transition";

import { Button } from "@/components/ui/button";

type TruncatedContentProps = {
  children: React.ReactNode;
  maxHeight?: number;
  disableReadMore?: boolean;
  className?: string;
  revealed?: boolean;
  onReveal?: () => void;
  height?: number;
  onChange?: (props: { showReveal: boolean; hideTruncate: boolean }) => void;
};

// For contents barely exceeding the max height, we allow a little more to be shown,
// so we don't crop tiny amount of contents.
const VIGGLE_ROOM = 100;

export const TruncatedContent = ({
  children,
  maxHeight,
  disableReadMore = false,
  className = "",
  revealed = false,
  onReveal,
  height,
  onChange,
}: TruncatedContentProps) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [showReveal, setShowReveal] = useState(false);
  const [hideTruncate, setHideTruncate] = useState(false);
  const [finalRevealed, setFinalRevealed] = useState(revealed);
  const onChangeRef = useRef(onChange);
  onChangeRef.current = onChange;

  useEffect(() => {
    if (!maxHeight || !contentRef.current) return;

    const finalHeight = height || contentRef.current.offsetHeight;
    setShowReveal(finalHeight >= maxHeight);
    setHideTruncate(finalHeight - maxHeight <= VIGGLE_ROOM);
  }, [height, maxHeight]);

  useEffect(() => {
    onChangeRef.current?.({ showReveal, hideTruncate });
  }, [showReveal, hideTruncate]);

  const handleRevealClick = () => {
    startViewTransition(() => {
      setShowReveal(false);
      setFinalRevealed(true);
      onReveal?.();
    });
  };

  const allowToReveal =
    !disableReadMore && showReveal && !finalRevealed && !hideTruncate;
  const truncateContent = !disableReadMore && !finalRevealed;

  return (
    <div className="relative">
      <div
        className="h-full overflow-hidden"
        style={{
          maxHeight:
            truncateContent && maxHeight ? maxHeight + VIGGLE_ROOM : undefined,
          maskImage: allowToReveal
            ? `linear-gradient(180deg, #000000FF calc(100% - 100px), #00000000 calc(100% - 50px))`
            : undefined,
        }}
      >
        <div ref={contentRef} className={className}>
          {children}
        </div>
      </div>
      {allowToReveal && (
        <Button
          onClick={handleRevealClick}
          variant="link"
          className="absolute bottom-0 left-0 pl-0"
        >
          Show More...
        </Button>
      )}
    </div>
  );
};
