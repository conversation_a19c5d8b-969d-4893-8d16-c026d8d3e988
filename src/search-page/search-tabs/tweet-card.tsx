import { AppResource, AppTag } from '../../core.types';

import { CardActions } from '@/components/CardActions/CardActions';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { cn } from '@/utils';
import { ExternalLinkIcon } from 'lucide-react';
import { Tweet } from 'react-tweet';
import { useLocalStorage } from 'usehooks-ts';
import { useRootContext } from '../../context/useRootContext';
import { DeleteAction } from '../resource-actions/delete-action';
import { EditAction } from '../resource-actions/edit-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';

type TweetCardProps = {
  resource: AppResource;
  className?: string;
};
// Maximum height for tweet cards before truncation

export const TweetCard = ({ resource, className }: TweetCardProps) => {
  const { tags, hasAdminRole } = useRootContext();
  const [isDarkMode] = useLocalStorage<boolean>('usehooks-ts-dark-mode', false);

  const tagsList = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean) as AppTag[];

  const tweetId = resource.url.split('/').pop();

  // Create a direct link to the tweet
  const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;

  return (
    <Card
      className={cn(
        'flex flex-col transition-all duration-200 hover:shadow-md relative h-full',
        'border border-border/60 hover:border-border/80',
        'group',
        className,
      )}
    >
      <GlowingEffect
        disabled={false}
        spread={40}
        glow={true}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={1.5}
      />

      <div className='relative'>
        <div
          className={cn(
            'p-3 pb-0 text-sm [&_*]:text-sm [&_p]:text-sm [&_span]:text-sm',
            {
              dark: isDarkMode,
              light: !isDarkMode,
            },
          )}
        >
          <Tweet id={tweetId as string} />
        </div>

        <CardActions
          resource={resource}
          variant='floating'
          showEditDelete={hasAdminRole}
        />
      </div>

      <div className='flex flex-col mt-auto'>
        <CardContent className='pt-0 pb-2'>
          <ResourceTags tags={tagsList} className='mt-2' />
        </CardContent>

        <CardFooter className='pt-0 pb-3 px-3 flex justify-between items-center border-t border-border/20'>
          <div className='flex items-center gap-1'>
            <a
              href={tweetUrl}
              target='_blank'
              rel='noopener noreferrer'
              className='p-1.5 text-muted-foreground hover:text-foreground rounded-full hover:bg-accent transition-colors'
            >
              <ExternalLinkIcon className='h-4 w-4' />
            </a>
            <div className='p-1.5 text-muted-foreground hover:text-foreground rounded-full hover:bg-accent transition-colors'>
              <ReadMarkAction appResourceId={resource.id} />
            </div>
          </div>

          <div className='flex items-center gap-1'>
            <EditAction resource={resource} />
            <DeleteAction resourceId={resource.id} />
          </div>
        </CardFooter>
      </div>
    </Card>
  );
};
